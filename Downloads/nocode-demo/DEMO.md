# Vue 低码平台演示指南

## 🚀 快速体验

### 1. 启动项目
```bash
npm install
npm run dev
```
访问 http://localhost:3000

### 2. 快速创建登录页面
1. 点击工具栏中的 **"模板"** 下拉菜单
2. 选择 **"登录页面模板"**
3. 系统自动生成完整的登录页面

### 3. 体验响应式设计
1. 点击 **"预览模式"**
2. 使用设备切换按钮：**桌面端** | **平板** | **手机**
3. 观察页面在不同设备上的适配效果

### 4. 导出源码
1. 点击 **"导出代码"** 按钮
2. 选择导出格式：
   - **Vue组件**: 可直接用于Vue项目
   - **HTML文件**: 独立的HTML页面
   - **项目配置**: JSON格式的项目备份
3. 点击 **"下载文件"** 或 **"复制代码"**

### 5. 导入项目
1. 点击 **"导入项目"** 按钮
2. 选择导入方式：
   - **文件导入**: 拖拽JSON配置文件
   - **文本导入**: 粘贴JSON配置内容
3. 预览导入内容后点击 **"导入"**

## 📱 移动端体验

### 在手机上访问
1. 确保手机和电脑在同一网络
2. 在终端查看Network地址
3. 在手机浏览器访问该地址

### 移动端特性
- **触摸优化**: 按钮尺寸符合移动端标准
- **布局自适应**: 组件面板和属性面板自动调整
- **字体优化**: 防止iOS自动缩放
- **交互优化**: 触摸反馈和手势支持

## 🎯 核心功能演示

### 拖拽编辑
1. 从左侧组件库拖拽组件到画布
2. 点击组件进行选中
3. 拖拽组件调整位置
4. 在右侧面板编辑属性

### 属性配置
- **基础属性**: 文本内容、占位符、按钮类型等
- **样式设置**: 位置、尺寸、颜色、边框等
- **实时预览**: 修改即时生效

### 预览功能
- **编辑模式**: 可拖拽、选中、编辑
- **预览模式**: 模拟真实用户交互
- **设备预览**: 桌面、平板、手机三种模式
- **独立预览**: 新窗口打开预览页面

## 🔧 高级功能

### 源码导出示例

**Vue组件导出**:
```vue
<template>
  <div class="page-container">
    <h2 style="position: absolute; left: 150px; top: 50px;">用户登录</h2>
    <el-input placeholder="请输入用户名" style="position: absolute; left: 150px; top: 120px;" />
    <!-- 更多组件... -->
  </div>
</template>

<script setup>
import { ref } from 'vue'
// 组件逻辑...
</script>
```

**HTML文件导出**:
```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Generated Page</title>
  <!-- Element Plus CDN -->
</head>
<body>
  <div id="app">
    <!-- 页面内容 -->
  </div>
  <!-- Vue和Element Plus脚本 -->
</body>
</html>
```

### 项目配置JSON
```json
{
  "version": "1.0.0",
  "components": [
    {
      "id": "component_1",
      "type": "text",
      "props": {
        "content": "用户登录",
        "tag": "h2"
      },
      "style": {
        "position": "absolute",
        "left": "150px",
        "top": "50px"
      }
    }
  ],
  "metadata": {
    "createdAt": "2024-01-01T00:00:00.000Z",
    "generator": "Vue NoCode Platform"
  }
}
```

## 🎨 自定义扩展

### 添加新组件
1. 在 `src/components/RenderComponents/` 创建组件
2. 在 `src/components/PropertyEditors/` 创建属性编辑器
3. 在 `src/components/ComponentLibrary.vue` 添加配置
4. 更新映射表和导出文件

### 自定义样式
在 `src/styles/global.scss` 中添加全局样式：
```scss
.custom-component {
  border: 2px solid #409eff;
  border-radius: 8px;
  
  @media (max-width: 768px) {
    border-width: 1px;
  }
}
```

## 💡 使用技巧

1. **快速定位**: 双击组件快速定位到属性面板
2. **批量操作**: 使用模板快速创建页面结构
3. **响应式测试**: 使用设备预览确保多端适配
4. **代码复用**: 导出组件后可在其他项目中使用
5. **项目备份**: 定期导出项目配置作为备份

## 🐛 常见问题

**Q: 组件拖拽不生效？**
A: 确保浏览器支持HTML5拖拽API，建议使用Chrome或Firefox

**Q: 移动端预览不准确？**
A: 使用浏览器开发者工具的设备模拟功能，或在真实设备上测试

**Q: 导出的代码无法运行？**
A: 确保目标项目已安装Vue和Element Plus依赖

**Q: 导入项目失败？**
A: 检查JSON格式是否正确，确保包含必要的字段

## 📞 技术支持

如有问题或建议，请查看项目README文件或提交Issue。

---

**享受低码开发的乐趣！** 🎉
