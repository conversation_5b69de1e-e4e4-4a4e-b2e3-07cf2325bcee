{"name": "vue-nocode-platform", "version": "1.0.0", "description": "A simple no-code platform built with Vue.js", "main": "index.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "serve": "vite preview"}, "dependencies": {"vue": "^3.3.4", "@vue/runtime-dom": "^3.3.4", "vue-router": "^4.2.4", "pinia": "^2.1.6", "element-plus": "^2.3.8", "@element-plus/icons-vue": "^2.1.0", "sortablejs": "^1.15.0", "vuedraggable": "^4.1.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.2.3", "vite": "^4.4.5", "sass": "^1.64.1"}, "keywords": ["vue", "no-code", "drag-drop", "visual-editor"], "author": "", "license": "MIT"}