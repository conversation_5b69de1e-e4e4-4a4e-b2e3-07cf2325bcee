import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useEditorStore = defineStore('editor', () => {
  // 画布中的组件列表
  const components = ref([])

  // 当前选中的组件ID
  const selectedComponentId = ref(null)

  // 组件ID计数器
  const componentIdCounter = ref(1)

  // 当前选中的组件
  const selectedComponent = computed(() => {
    return components.value.find(comp => comp.id === selectedComponentId.value)
  })

  // 添加组件到画布
  const addComponent = (componentType, position = { x: 100, y: 100 }) => {
    const newComponent = {
      id: `component_${componentIdCounter.value++}`,
      type: componentType,
      props: getDefaultProps(componentType),
      style: {
        position: 'absolute',
        left: position.x + 'px',
        top: position.y + 'px',
        ...getDefaultStyle(componentType)
      }
    }

    components.value.push(newComponent)
    selectedComponentId.value = newComponent.id
    return newComponent
  }

  // 删除组件
  const removeComponent = (componentId) => {
    const index = components.value.findIndex(comp => comp.id === componentId)
    if (index > -1) {
      components.value.splice(index, 1)
      if (selectedComponentId.value === componentId) {
        selectedComponentId.value = null
      }
    }
  }

  // 更新组件属性
  const updateComponentProps = (componentId, newProps) => {
    const component = components.value.find(comp => comp.id === componentId)
    if (component) {
      component.props = { ...component.props, ...newProps }
    }
  }

  // 更新组件样式
  const updateComponentStyle = (componentId, newStyle) => {
    const component = components.value.find(comp => comp.id === componentId)
    if (component) {
      component.style = { ...component.style, ...newStyle }
    }
  }

  // 选中组件
  const selectComponent = (componentId) => {
    selectedComponentId.value = componentId
  }

  // 清空选择
  const clearSelection = () => {
    selectedComponentId.value = null
  }

  // 清空画布
  const clearCanvas = () => {
    components.value = []
    selectedComponentId.value = null
    componentIdCounter.value = 1
  }

  // 加载登录页面模板
  const loadLoginTemplate = () => {
    clearCanvas()

    // 添加标题
    addComponent('text', { x: 150, y: 50 })
    updateComponentProps(`component_1`, {
      content: '用户登录',
      tag: 'h2',
      fontSize: '24px',
      color: '#333',
      textAlign: 'center'
    })
    updateComponentStyle(`component_1`, {
      width: '300px',
      fontWeight: 'bold'
    })

    // 添加用户名输入框
    addComponent('input', { x: 150, y: 120 })
    updateComponentProps(`component_2`, {
      placeholder: '请输入用户名',
      type: 'text'
    })
    updateComponentStyle(`component_2`, {
      width: '300px',
      height: '40px'
    })

    // 添加密码输入框
    addComponent('input', { x: 150, y: 180 })
    updateComponentProps(`component_3`, {
      placeholder: '请输入密码',
      type: 'password'
    })
    updateComponentStyle(`component_3`, {
      width: '300px',
      height: '40px'
    })

    // 添加登录按钮
    addComponent('button', { x: 150, y: 240 })
    updateComponentProps(`component_4`, {
      text: '登录',
      type: 'primary'
    })
    updateComponentStyle(`component_4`, {
      width: '300px',
      height: '40px'
    })

    // 添加注册链接
    addComponent('text', { x: 150, y: 300 })
    updateComponentProps(`component_5`, {
      content: '还没有账号？<a href="#" style="color: #409eff;">立即注册</a>',
      tag: 'p',
      fontSize: '14px',
      color: '#666',
      textAlign: 'center'
    })
    updateComponentStyle(`component_5`, {
      width: '300px'
    })

    selectedComponentId.value = null
  }

  // 获取默认属性
  function getDefaultProps(componentType) {
    const defaultProps = {
      'input': {
        placeholder: '请输入内容',
        value: '',
        type: 'text'
      },
      'button': {
        text: '按钮',
        type: 'primary'
      },
      'text': {
        content: '文本内容',
        tag: 'div'
      },
      'image': {
        src: '',
        alt: '图片'
      }
    }
    return defaultProps[componentType] || {}
  }

  // 获取默认样式
  function getDefaultStyle(componentType) {
    const defaultStyles = {
      'input': {
        width: '200px',
        height: '32px'
      },
      'button': {
        width: '80px',
        height: '32px'
      },
      'text': {
        fontSize: '14px',
        color: '#333'
      },
      'image': {
        width: '100px',
        height: '100px'
      }
    }
    return defaultStyles[componentType] || {}
  }

  return {
    components,
    selectedComponentId,
    selectedComponent,
    addComponent,
    removeComponent,
    updateComponentProps,
    updateComponentStyle,
    selectComponent,
    clearSelection,
    clearCanvas,
    loadLoginTemplate
  }
})
