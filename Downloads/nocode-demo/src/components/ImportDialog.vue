<template>
  <el-dialog
    v-model="visible"
    title="导入项目"
    width="60%"
    :before-close="handleClose"
  >
    <div class="import-dialog">
      <!-- 导入方式选择 -->
      <div class="import-types">
        <el-radio-group v-model="importType">
          <el-radio-button label="file">文件导入</el-radio-button>
          <el-radio-button label="text">文本导入</el-radio-button>
        </el-radio-group>
      </div>
      
      <!-- 文件导入 -->
      <div v-if="importType === 'file'" class="file-import">
        <el-upload
          ref="uploadRef"
          :auto-upload="false"
          :show-file-list="true"
          :limit="1"
          accept=".json"
          @change="handleFileChange"
          drag
        >
          <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
          <div class="el-upload__text">
            将JSON配置文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              只能上传JSON格式的项目配置文件
            </div>
          </template>
        </el-upload>
      </div>
      
      <!-- 文本导入 -->
      <div v-if="importType === 'text'" class="text-import">
        <el-input
          v-model="importText"
          type="textarea"
          :rows="12"
          placeholder="请粘贴项目配置JSON内容..."
          @input="validateJSON"
        />
        <div v-if="jsonError" class="error-message">
          <el-icon><Warning /></el-icon>
          {{ jsonError }}
        </div>
      </div>
      
      <!-- 预览区域 -->
      <div v-if="previewData" class="preview-section">
        <h4>导入预览</h4>
        <div class="preview-info">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="组件数量">
              {{ previewData.components?.length || 0 }}
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ formatDate(previewData.metadata?.createdAt) }}
            </el-descriptions-item>
            <el-descriptions-item label="生成器">
              {{ previewData.metadata?.generator || '未知' }}
            </el-descriptions-item>
            <el-descriptions-item label="版本">
              {{ previewData.version || '未知' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
        
        <div class="component-list">
          <h5>组件列表：</h5>
          <el-tag
            v-for="(component, index) in previewData.components"
            :key="index"
            class="component-tag"
            :type="getComponentTagType(component.type)"
          >
            {{ getComponentDisplayName(component.type) }}
          </el-tag>
        </div>
      </div>
      
      <!-- 导入选项 -->
      <div v-if="previewData" class="import-options">
        <el-checkbox v-model="replaceExisting">
          替换现有项目（清空当前画布）
        </el-checkbox>
      </div>
    </div>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          :disabled="!previewData || !!jsonError"
          @click="handleImport"
        >
          导入
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { UploadFilled, Warning } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'import'])

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const importType = ref('file')
const importText = ref('')
const previewData = ref(null)
const jsonError = ref('')
const replaceExisting = ref(true)
const uploadRef = ref()

// 处理文件变化
const handleFileChange = (file) => {
  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const content = e.target.result
      const data = JSON.parse(content)
      validateAndSetPreviewData(data)
    } catch (error) {
      ElMessage.error('文件格式错误，请上传有效的JSON文件')
      jsonError.value = '文件格式错误'
    }
  }
  reader.readAsText(file.raw)
}

// 验证JSON
const validateJSON = () => {
  jsonError.value = ''
  previewData.value = null
  
  if (!importText.value.trim()) {
    return
  }
  
  try {
    const data = JSON.parse(importText.value)
    validateAndSetPreviewData(data)
  } catch (error) {
    jsonError.value = 'JSON格式错误：' + error.message
  }
}

// 验证并设置预览数据
const validateAndSetPreviewData = (data) => {
  // 验证数据结构
  if (!data || typeof data !== 'object') {
    jsonError.value = '数据格式错误'
    return
  }
  
  if (!Array.isArray(data.components)) {
    jsonError.value = '缺少components字段或格式错误'
    return
  }
  
  // 验证组件数据
  for (let i = 0; i < data.components.length; i++) {
    const component = data.components[i]
    if (!component.id || !component.type || !component.props || !component.style) {
      jsonError.value = `组件${i + 1}数据不完整`
      return
    }
  }
  
  previewData.value = data
  jsonError.value = ''
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '未知'
  try {
    return new Date(dateString).toLocaleString('zh-CN')
  } catch {
    return '格式错误'
  }
}

// 获取组件标签类型
const getComponentTagType = (type) => {
  const typeMap = {
    text: 'info',
    input: 'success',
    button: 'warning',
    image: 'danger'
  }
  return typeMap[type] || 'info'
}

// 获取组件显示名称
const getComponentDisplayName = (type) => {
  const nameMap = {
    text: '文本',
    input: '输入框',
    button: '按钮',
    image: '图片'
  }
  return nameMap[type] || type
}

// 处理导入
const handleImport = () => {
  if (!previewData.value) {
    ElMessage.error('没有可导入的数据')
    return
  }
  
  emit('import', {
    data: previewData.value,
    replaceExisting: replaceExisting.value
  })
  
  ElMessage.success('项目导入成功')
  handleClose()
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  // 重置状态
  importType.value = 'file'
  importText.value = ''
  previewData.value = null
  jsonError.value = ''
  replaceExisting.value = true
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
}
</script>

<style scoped lang="scss">
.import-dialog {
  .import-types {
    margin-bottom: 20px;
    text-align: center;
  }
  
  .file-import {
    margin-bottom: 20px;
  }
  
  .text-import {
    margin-bottom: 20px;
    
    .error-message {
      margin-top: 8px;
      color: #f56c6c;
      font-size: 14px;
      display: flex;
      align-items: center;
      gap: 4px;
    }
  }
  
  .preview-section {
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;
    
    h4 {
      margin: 0 0 16px 0;
      color: #333;
    }
    
    .preview-info {
      margin-bottom: 16px;
    }
    
    .component-list {
      h5 {
        margin: 0 0 8px 0;
        color: #666;
        font-size: 14px;
      }
      
      .component-tag {
        margin-right: 8px;
        margin-bottom: 8px;
      }
    }
  }
  
  .import-options {
    padding: 16px;
    background: #fff7e6;
    border: 1px solid #ffd591;
    border-radius: 6px;
    
    .el-checkbox {
      color: #d46b08;
    }
  }
}

:deep(.el-upload-dragger) {
  width: 100%;
}
</style>
