import TextComponent from './TextComponent.vue'
import InputComponent from './InputComponent.vue'
import ButtonComponent from './ButtonComponent.vue'
import ImageComponent from './ImageComponent.vue'

// 组件映射表
export const componentMap = {
  text: TextComponent,
  input: InputComponent,
  button: ButtonComponent,
  image: ImageComponent
}

// 获取组件
export const getComponent = (type) => {
  return componentMap[type] || null
}

// 导出所有组件
export {
  TextComponent,
  InputComponent,
  ButtonComponent,
  ImageComponent
}
