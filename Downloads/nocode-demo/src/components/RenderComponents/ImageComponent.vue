<template>
  <div class="image-component">
    <img
      v-if="props.src"
      :src="props.src"
      :alt="props.alt"
      :style="imageStyle"
      @error="handleError"
      @load="handleLoad"
    />
    <div
      v-else
      class="image-placeholder"
      :style="imageStyle"
    >
      <el-icon size="24">
        <Picture />
      </el-icon>
      <span>点击上传图片</span>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { Picture } from '@element-plus/icons-vue'

const props = defineProps({
  src: {
    type: String,
    default: ''
  },
  alt: {
    type: String,
    default: '图片'
  },
  width: String,
  height: String,
  objectFit: {
    type: String,
    default: 'cover'
  }
})

const emit = defineEmits(['error', 'load'])

const imageStyle = computed(() => {
  const style = {
    objectFit: props.objectFit
  }
  
  if (props.width) style.width = props.width
  if (props.height) style.height = props.height
  
  return style
})

const handleError = (event) => {
  emit('error', event)
}

const handleLoad = (event) => {
  emit('load', event)
}
</script>

<style scoped>
.image-component {
  display: inline-block;
  
  img {
    max-width: 100%;
    height: auto;
    display: block;
  }
  
  .image-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #f5f5f5;
    border: 1px dashed #ccc;
    color: #999;
    min-width: 100px;
    min-height: 100px;
    
    span {
      margin-top: 8px;
      font-size: 12px;
    }
  }
}
</style>
