<template>
  <el-button
    :type="props.type"
    :size="props.size"
    :disabled="props.disabled"
    :loading="props.loading"
    :plain="props.plain"
    :round="props.round"
    :circle="props.circle"
    class="button-component"
    @click="handleClick"
  >
    {{ props.text || '按钮' }}
  </el-button>
</template>

<script setup>
const props = defineProps({
  text: {
    type: String,
    default: '按钮'
  },
  type: {
    type: String,
    default: 'primary'
  },
  size: {
    type: String,
    default: 'default'
  },
  disabled: {
    type: Boolean,
    default: false
  },
  loading: {
    type: Boolean,
    default: false
  },
  plain: {
    type: Boolean,
    default: false
  },
  round: {
    type: Boolean,
    default: false
  },
  circle: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['click'])

const handleClick = (event) => {
  emit('click', event)
}
</script>

<style scoped>
.button-component {
  /* 按钮样式可以在这里自定义 */
}
</style>
