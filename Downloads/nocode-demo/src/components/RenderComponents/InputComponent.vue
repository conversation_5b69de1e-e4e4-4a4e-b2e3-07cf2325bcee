<template>
  <el-input
    v-model="inputValue"
    :placeholder="props.placeholder"
    :type="props.type"
    :disabled="props.disabled"
    :size="props.size"
    class="input-component"
    @input="handleInput"
    @change="handleChange"
  />
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  value: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '请输入内容'
  },
  type: {
    type: String,
    default: 'text'
  },
  disabled: {
    type: Boolean,
    default: false
  },
  size: {
    type: String,
    default: 'default'
  }
})

const emit = defineEmits(['update:value', 'input', 'change'])

const inputValue = ref(props.value)

watch(() => props.value, (newValue) => {
  inputValue.value = newValue
})

const handleInput = (value) => {
  emit('update:value', value)
  emit('input', value)
}

const handleChange = (value) => {
  emit('change', value)
}
</script>

<style scoped>
.input-component {
  width: 100%;
}
</style>
