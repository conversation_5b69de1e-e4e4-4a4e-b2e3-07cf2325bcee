<template>
  <component
    :is="props.tag || 'div'"
    :style="computedStyle"
    class="text-component"
    v-html="props.content || '文本内容'"
  />
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  content: {
    type: String,
    default: '文本内容'
  },
  tag: {
    type: String,
    default: 'div'
  },
  fontSize: String,
  color: String,
  fontWeight: String,
  textAlign: String
})

const computedStyle = computed(() => {
  const style = {}
  
  if (props.fontSize) style.fontSize = props.fontSize
  if (props.color) style.color = props.color
  if (props.fontWeight) style.fontWeight = props.fontWeight
  if (props.textAlign) style.textAlign = props.textAlign
  
  return style
})
</script>

<style scoped>
.text-component {
  word-wrap: break-word;
  white-space: pre-wrap;
}
</style>
