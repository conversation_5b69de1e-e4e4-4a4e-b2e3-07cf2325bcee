<template>
  <div class="image-property-editor">
    <div class="property-item">
      <label>图片地址</label>
      <el-input
        v-model="localProps.src"
        placeholder="请输入图片URL"
        @input="updateProps"
      />
    </div>
    
    <div class="property-item">
      <label>替代文本</label>
      <el-input
        v-model="localProps.alt"
        placeholder="请输入替代文本"
        @input="updateProps"
      />
    </div>
    
    <div class="property-item">
      <label>图片宽度</label>
      <el-input
        v-model="localProps.width"
        placeholder="100px 或 auto"
        @input="updateProps"
      />
    </div>
    
    <div class="property-item">
      <label>图片高度</label>
      <el-input
        v-model="localProps.height"
        placeholder="100px 或 auto"
        @input="updateProps"
      />
    </div>
    
    <div class="property-item">
      <label>适应方式</label>
      <el-select
        v-model="localProps.objectFit"
        placeholder="选择适应方式"
        @change="updateProps"
      >
        <el-option label="填充" value="fill" />
        <el-option label="包含" value="contain" />
        <el-option label="覆盖" value="cover" />
        <el-option label="缩放" value="scale-down" />
        <el-option label="无" value="none" />
      </el-select>
    </div>
    
    <div class="property-item">
      <label>快速设置</label>
      <div class="quick-actions">
        <el-button
          size="small"
          @click="setDefaultImage"
        >
          使用默认图片
        </el-button>
        <el-button
          size="small"
          @click="clearImage"
        >
          清空图片
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update'])

const localProps = ref({
  src: '',
  alt: '图片',
  width: '',
  height: '',
  objectFit: 'cover'
})

// 监听组件变化
watch(() => props.component, (newComponent) => {
  if (newComponent && newComponent.props) {
    localProps.value = { ...localProps.value, ...newComponent.props }
  }
}, { immediate: true })

// 更新属性
const updateProps = () => {
  emit('update', { ...localProps.value })
}

// 设置默认图片
const setDefaultImage = () => {
  localProps.value.src = 'https://via.placeholder.com/200x150?text=示例图片'
  updateProps()
}

// 清空图片
const clearImage = () => {
  localProps.value.src = ''
  updateProps()
}
</script>

<style scoped lang="scss">
.image-property-editor {
  .property-item {
    margin-bottom: 16px;
    
    label {
      display: block;
      font-size: 14px;
      color: #333;
      margin-bottom: 8px;
    }
    
    .quick-actions {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }
  }
}
</style>
