<template>
  <div class="button-property-editor">
    <div class="property-item">
      <label>按钮文本</label>
      <el-input
        v-model="localProps.text"
        placeholder="请输入按钮文本"
        @input="updateProps"
      />
    </div>
    
    <div class="property-item">
      <label>按钮类型</label>
      <el-select
        v-model="localProps.type"
        placeholder="选择类型"
        @change="updateProps"
      >
        <el-option label="主要按钮" value="primary" />
        <el-option label="成功按钮" value="success" />
        <el-option label="信息按钮" value="info" />
        <el-option label="警告按钮" value="warning" />
        <el-option label="危险按钮" value="danger" />
        <el-option label="默认按钮" value="default" />
      </el-select>
    </div>
    
    <div class="property-item">
      <label>按钮尺寸</label>
      <el-select
        v-model="localProps.size"
        placeholder="选择尺寸"
        @change="updateProps"
      >
        <el-option label="大" value="large" />
        <el-option label="默认" value="default" />
        <el-option label="小" value="small" />
      </el-select>
    </div>
    
    <div class="property-item">
      <label>按钮样式</label>
      <div class="checkbox-group">
        <el-checkbox
          v-model="localProps.plain"
          @change="updateProps"
        >
          朴素按钮
        </el-checkbox>
        <el-checkbox
          v-model="localProps.round"
          @change="updateProps"
        >
          圆角按钮
        </el-checkbox>
        <el-checkbox
          v-model="localProps.circle"
          @change="updateProps"
        >
          圆形按钮
        </el-checkbox>
      </div>
    </div>
    
    <div class="property-item">
      <label>按钮状态</label>
      <div class="checkbox-group">
        <el-checkbox
          v-model="localProps.disabled"
          @change="updateProps"
        >
          禁用
        </el-checkbox>
        <el-checkbox
          v-model="localProps.loading"
          @change="updateProps"
        >
          加载中
        </el-checkbox>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update'])

const localProps = ref({
  text: '按钮',
  type: 'primary',
  size: 'default',
  plain: false,
  round: false,
  circle: false,
  disabled: false,
  loading: false
})

// 监听组件变化
watch(() => props.component, (newComponent) => {
  if (newComponent && newComponent.props) {
    localProps.value = { ...localProps.value, ...newComponent.props }
  }
}, { immediate: true })

// 更新属性
const updateProps = () => {
  emit('update', { ...localProps.value })
}
</script>

<style scoped lang="scss">
.button-property-editor {
  .property-item {
    margin-bottom: 16px;
    
    label {
      display: block;
      font-size: 14px;
      color: #333;
      margin-bottom: 8px;
    }
    
    .checkbox-group {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
  }
}
</style>
