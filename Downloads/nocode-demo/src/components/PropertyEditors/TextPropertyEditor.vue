<template>
  <div class="text-property-editor">
    <div class="property-item">
      <label>文本内容</label>
      <el-input
        v-model="localProps.content"
        type="textarea"
        :rows="3"
        placeholder="请输入文本内容"
        @input="updateProps"
      />
    </div>
    
    <div class="property-item">
      <label>HTML标签</label>
      <el-select
        v-model="localProps.tag"
        placeholder="选择标签"
        @change="updateProps"
      >
        <el-option label="div" value="div" />
        <el-option label="p" value="p" />
        <el-option label="span" value="span" />
        <el-option label="h1" value="h1" />
        <el-option label="h2" value="h2" />
        <el-option label="h3" value="h3" />
        <el-option label="h4" value="h4" />
        <el-option label="h5" value="h5" />
        <el-option label="h6" value="h6" />
      </el-select>
    </div>
    
    <div class="property-item">
      <label>字体大小</label>
      <el-input
        v-model="localProps.fontSize"
        placeholder="14px"
        @input="updateProps"
      />
    </div>
    
    <div class="property-item">
      <label>字体颜色</label>
      <el-color-picker
        v-model="localProps.color"
        @change="updateProps"
      />
    </div>
    
    <div class="property-item">
      <label>字体粗细</label>
      <el-select
        v-model="localProps.fontWeight"
        placeholder="选择粗细"
        @change="updateProps"
      >
        <el-option label="正常" value="normal" />
        <el-option label="粗体" value="bold" />
        <el-option label="100" value="100" />
        <el-option label="200" value="200" />
        <el-option label="300" value="300" />
        <el-option label="400" value="400" />
        <el-option label="500" value="500" />
        <el-option label="600" value="600" />
        <el-option label="700" value="700" />
        <el-option label="800" value="800" />
        <el-option label="900" value="900" />
      </el-select>
    </div>
    
    <div class="property-item">
      <label>文本对齐</label>
      <el-select
        v-model="localProps.textAlign"
        placeholder="选择对齐方式"
        @change="updateProps"
      >
        <el-option label="左对齐" value="left" />
        <el-option label="居中" value="center" />
        <el-option label="右对齐" value="right" />
        <el-option label="两端对齐" value="justify" />
      </el-select>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update'])

const localProps = ref({
  content: '',
  tag: 'div',
  fontSize: '',
  color: '',
  fontWeight: '',
  textAlign: ''
})

// 监听组件变化
watch(() => props.component, (newComponent) => {
  if (newComponent && newComponent.props) {
    localProps.value = { ...localProps.value, ...newComponent.props }
  }
}, { immediate: true })

// 更新属性
const updateProps = () => {
  emit('update', { ...localProps.value })
}
</script>

<style scoped lang="scss">
.text-property-editor {
  .property-item {
    margin-bottom: 16px;
    
    label {
      display: block;
      font-size: 14px;
      color: #333;
      margin-bottom: 8px;
    }
  }
}
</style>
