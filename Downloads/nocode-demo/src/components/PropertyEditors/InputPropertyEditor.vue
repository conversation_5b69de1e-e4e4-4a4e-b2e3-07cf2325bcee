<template>
  <div class="input-property-editor">
    <div class="property-item">
      <label>占位符文本</label>
      <el-input
        v-model="localProps.placeholder"
        placeholder="请输入占位符"
        @input="updateProps"
      />
    </div>
    
    <div class="property-item">
      <label>默认值</label>
      <el-input
        v-model="localProps.value"
        placeholder="请输入默认值"
        @input="updateProps"
      />
    </div>
    
    <div class="property-item">
      <label>输入类型</label>
      <el-select
        v-model="localProps.type"
        placeholder="选择类型"
        @change="updateProps"
      >
        <el-option label="文本" value="text" />
        <el-option label="密码" value="password" />
        <el-option label="邮箱" value="email" />
        <el-option label="数字" value="number" />
        <el-option label="电话" value="tel" />
        <el-option label="网址" value="url" />
      </el-select>
    </div>
    
    <div class="property-item">
      <label>尺寸</label>
      <el-select
        v-model="localProps.size"
        placeholder="选择尺寸"
        @change="updateProps"
      >
        <el-option label="大" value="large" />
        <el-option label="默认" value="default" />
        <el-option label="小" value="small" />
      </el-select>
    </div>
    
    <div class="property-item">
      <label>状态</label>
      <el-checkbox
        v-model="localProps.disabled"
        @change="updateProps"
      >
        禁用
      </el-checkbox>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update'])

const localProps = ref({
  placeholder: '请输入内容',
  value: '',
  type: 'text',
  size: 'default',
  disabled: false
})

// 监听组件变化
watch(() => props.component, (newComponent) => {
  if (newComponent && newComponent.props) {
    localProps.value = { ...localProps.value, ...newComponent.props }
  }
}, { immediate: true })

// 更新属性
const updateProps = () => {
  emit('update', { ...localProps.value })
}
</script>

<style scoped lang="scss">
.input-property-editor {
  .property-item {
    margin-bottom: 16px;
    
    label {
      display: block;
      font-size: 14px;
      color: #333;
      margin-bottom: 8px;
    }
  }
}
</style>
