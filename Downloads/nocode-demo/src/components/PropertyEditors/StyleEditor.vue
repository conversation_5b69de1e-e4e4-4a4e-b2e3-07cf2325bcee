<template>
  <div class="style-editor">
    <!-- 位置设置 -->
    <div class="property-section">
      <h4>位置</h4>
      <el-row :gutter="12">
        <el-col :span="12">
          <div class="property-item">
            <label>X坐标</label>
            <el-input-number
              v-model="position.x"
              :min="0"
              size="small"
              @change="updatePosition"
            />
          </div>
        </el-col>
        <el-col :span="12">
          <div class="property-item">
            <label>Y坐标</label>
            <el-input-number
              v-model="position.y"
              :min="0"
              size="small"
              @change="updatePosition"
            />
          </div>
        </el-col>
      </el-row>
    </div>
    
    <!-- 尺寸设置 -->
    <div class="property-section">
      <h4>尺寸</h4>
      <el-row :gutter="12">
        <el-col :span="12">
          <div class="property-item">
            <label>宽度</label>
            <el-input
              v-model="size.width"
              size="small"
              placeholder="auto"
              @input="updateSize"
            />
          </div>
        </el-col>
        <el-col :span="12">
          <div class="property-item">
            <label>高度</label>
            <el-input
              v-model="size.height"
              size="small"
              placeholder="auto"
              @input="updateSize"
            />
          </div>
        </el-col>
      </el-row>
    </div>
    
    <!-- 外观设置 -->
    <div class="property-section">
      <h4>外观</h4>
      <div class="property-item">
        <label>背景色</label>
        <el-color-picker
          v-model="appearance.backgroundColor"
          size="small"
          @change="updateAppearance"
        />
      </div>
      <div class="property-item">
        <label>边框</label>
        <el-input
          v-model="appearance.border"
          size="small"
          placeholder="1px solid #ccc"
          @input="updateAppearance"
        />
      </div>
      <div class="property-item">
        <label>圆角</label>
        <el-input
          v-model="appearance.borderRadius"
          size="small"
          placeholder="0px"
          @input="updateAppearance"
        />
      </div>
      <div class="property-item">
        <label>透明度</label>
        <el-slider
          v-model="appearance.opacity"
          :min="0"
          :max="1"
          :step="0.1"
          @change="updateAppearance"
        />
      </div>
    </div>
    
    <!-- 间距设置 -->
    <div class="property-section">
      <h4>间距</h4>
      <div class="property-item">
        <label>内边距</label>
        <el-input
          v-model="spacing.padding"
          size="small"
          placeholder="0px"
          @input="updateSpacing"
        />
      </div>
      <div class="property-item">
        <label>外边距</label>
        <el-input
          v-model="spacing.margin"
          size="small"
          placeholder="0px"
          @input="updateSpacing"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update'])

// 位置
const position = ref({
  x: 0,
  y: 0
})

// 尺寸
const size = ref({
  width: '',
  height: ''
})

// 外观
const appearance = ref({
  backgroundColor: '',
  border: '',
  borderRadius: '',
  opacity: 1
})

// 间距
const spacing = ref({
  padding: '',
  margin: ''
})

// 监听组件变化，更新表单数据
watch(() => props.component, (newComponent) => {
  if (newComponent && newComponent.style) {
    const style = newComponent.style
    
    // 解析位置
    position.value.x = parseInt(style.left) || 0
    position.value.y = parseInt(style.top) || 0
    
    // 解析尺寸
    size.value.width = style.width || ''
    size.value.height = style.height || ''
    
    // 解析外观
    appearance.value.backgroundColor = style.backgroundColor || ''
    appearance.value.border = style.border || ''
    appearance.value.borderRadius = style.borderRadius || ''
    appearance.value.opacity = parseFloat(style.opacity) || 1
    
    // 解析间距
    spacing.value.padding = style.padding || ''
    spacing.value.margin = style.margin || ''
  }
}, { immediate: true })

// 更新位置
const updatePosition = () => {
  emit('update', {
    left: position.value.x + 'px',
    top: position.value.y + 'px'
  })
}

// 更新尺寸
const updateSize = () => {
  const updates = {}
  if (size.value.width) updates.width = size.value.width
  if (size.value.height) updates.height = size.value.height
  emit('update', updates)
}

// 更新外观
const updateAppearance = () => {
  const updates = {}
  if (appearance.value.backgroundColor) updates.backgroundColor = appearance.value.backgroundColor
  if (appearance.value.border) updates.border = appearance.value.border
  if (appearance.value.borderRadius) updates.borderRadius = appearance.value.borderRadius
  if (appearance.value.opacity !== 1) updates.opacity = appearance.value.opacity
  emit('update', updates)
}

// 更新间距
const updateSpacing = () => {
  const updates = {}
  if (spacing.value.padding) updates.padding = spacing.value.padding
  if (spacing.value.margin) updates.margin = spacing.value.margin
  emit('update', updates)
}
</script>

<style scoped lang="scss">
.style-editor {
  .property-section {
    margin-bottom: 20px;
    
    h4 {
      font-size: 14px;
      color: #333;
      margin-bottom: 12px;
      padding-bottom: 6px;
      border-bottom: 1px solid #eee;
    }
    
    .property-item {
      margin-bottom: 12px;
      
      label {
        display: block;
        font-size: 12px;
        color: #666;
        margin-bottom: 6px;
      }
    }
  }
}
</style>
