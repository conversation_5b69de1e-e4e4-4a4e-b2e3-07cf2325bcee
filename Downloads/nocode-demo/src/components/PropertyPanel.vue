<template>
  <div class="property-panel">
    <div class="panel-header">
      <h3>属性配置</h3>
    </div>
    
    <div v-if="!selectedComponent" class="no-selection">
      <el-empty description="请选择一个组件进行配置" />
    </div>
    
    <div v-else class="property-content">
      <!-- 基础信息 -->
      <el-collapse v-model="activeNames" accordion>
        <el-collapse-item title="基础属性" name="basic">
          <div class="property-group">
            <component
              :is="getPropertyEditor(selectedComponent.type)"
              :component="selectedComponent"
              @update="handlePropertyUpdate"
            />
          </div>
        </el-collapse-item>
        
        <el-collapse-item title="样式设置" name="style">
          <div class="property-group">
            <StyleEditor
              :component="selectedComponent"
              @update="handleStyleUpdate"
            />
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useEditorStore } from '@/stores/editor'
import StyleEditor from './PropertyEditors/StyleEditor.vue'
import TextPropertyEditor from './PropertyEditors/TextPropertyEditor.vue'
import InputPropertyEditor from './PropertyEditors/InputPropertyEditor.vue'
import ButtonPropertyEditor from './PropertyEditors/ButtonPropertyEditor.vue'
import ImagePropertyEditor from './PropertyEditors/ImagePropertyEditor.vue'

const editorStore = useEditorStore()
const activeNames = ref(['basic'])

const selectedComponent = computed(() => editorStore.selectedComponent)

// 属性编辑器映射
const propertyEditorMap = {
  text: TextPropertyEditor,
  input: InputPropertyEditor,
  button: ButtonPropertyEditor,
  image: ImagePropertyEditor
}

const getPropertyEditor = (componentType) => {
  return propertyEditorMap[componentType] || 'div'
}

// 处理属性更新
const handlePropertyUpdate = (newProps) => {
  if (selectedComponent.value) {
    editorStore.updateComponentProps(selectedComponent.value.id, newProps)
  }
}

// 处理样式更新
const handleStyleUpdate = (newStyle) => {
  if (selectedComponent.value) {
    editorStore.updateComponentStyle(selectedComponent.value.id, newStyle)
  }
}
</script>

<style scoped lang="scss">
.property-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .panel-header {
    padding: 16px;
    border-bottom: 1px solid #e0e0e0;
    
    h3 {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin: 0;
    }
  }
  
  .no-selection {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
  }
  
  .property-content {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    
    .property-group {
      .property-item {
        margin-bottom: 16px;
        
        .property-label {
          font-size: 14px;
          color: #333;
          margin-bottom: 8px;
          display: block;
        }
      }
    }
  }
}

:deep(.el-collapse) {
  border: none;
  
  .el-collapse-item__header {
    background: #f8f9fa;
    border: none;
    padding: 0 12px;
    font-weight: 500;
  }
  
  .el-collapse-item__content {
    padding: 16px 12px;
    border: none;
  }
}
</style>
