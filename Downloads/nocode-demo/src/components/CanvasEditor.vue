<template>
  <div
    class="canvas-editor"
    @drop="handleDrop"
    @dragover="handleDragOver"
    @click="handleCanvasClick"
  >
    <div class="canvas-content" ref="canvasRef">
      <!-- 渲染所有组件 -->
      <div
        v-for="component in components"
        :key="component.id"
        :class="[
          'component-wrapper',
          { 'selected': selectedComponentId === component.id }
        ]"
        :style="component.style"
        @click.stop="selectComponent(component.id)"
        @mousedown="handleMouseDown($event, component.id)"
      >
        <component
          :is="getComponent(component.type)"
          v-bind="component.props"
          class="rendered-component"
        />
        
        <!-- 选中状态的操作按钮 -->
        <div
          v-if="selectedComponentId === component.id"
          class="component-actions"
        >
          <el-button
            size="small"
            type="danger"
            :icon="Delete"
            circle
            @click.stop="removeComponent(component.id)"
          />
        </div>
      </div>
      
      <!-- 拖拽时的占位符 -->
      <div
        v-if="isDragging"
        class="drop-placeholder"
        :style="placeholderStyle"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useEditorStore } from '@/stores/editor'
import { getComponent } from '@/components/RenderComponents'
import { Delete } from '@element-plus/icons-vue'

const editorStore = useEditorStore()
const canvasRef = ref(null)

// 从store中获取数据
const components = computed(() => editorStore.components)
const selectedComponentId = computed(() => editorStore.selectedComponentId)

// 拖拽相关状态
const isDragging = ref(false)
const dragPosition = ref({ x: 0, y: 0 })
const placeholderStyle = computed(() => ({
  position: 'absolute',
  left: dragPosition.value.x + 'px',
  top: dragPosition.value.y + 'px',
  width: '100px',
  height: '40px'
}))

// 组件拖拽相关
const isDraggingComponent = ref(false)
const dragOffset = ref({ x: 0, y: 0 })

// 处理拖拽悬停
const handleDragOver = (event) => {
  event.preventDefault()
  event.dataTransfer.dropEffect = 'copy'
  
  const rect = canvasRef.value.getBoundingClientRect()
  dragPosition.value = {
    x: event.clientX - rect.left,
    y: event.clientY - rect.top
  }
  
  isDragging.value = true
}

// 处理放置
const handleDrop = (event) => {
  event.preventDefault()
  isDragging.value = false
  
  const componentType = event.dataTransfer.getData('component-type')
  if (!componentType) return
  
  const rect = canvasRef.value.getBoundingClientRect()
  const position = {
    x: event.clientX - rect.left,
    y: event.clientY - rect.top
  }
  
  editorStore.addComponent(componentType, position)
}

// 选中组件
const selectComponent = (componentId) => {
  editorStore.selectComponent(componentId)
}

// 删除组件
const removeComponent = (componentId) => {
  editorStore.removeComponent(componentId)
}

// 点击画布空白区域
const handleCanvasClick = () => {
  editorStore.clearSelection()
}

// 处理组件拖拽开始
const handleMouseDown = (event, componentId) => {
  if (event.button !== 0) return // 只处理左键
  
  event.preventDefault()
  isDraggingComponent.value = true
  
  const component = components.value.find(comp => comp.id === componentId)
  if (!component) return
  
  const rect = canvasRef.value.getBoundingClientRect()
  const componentRect = event.target.closest('.component-wrapper').getBoundingClientRect()
  
  dragOffset.value = {
    x: event.clientX - componentRect.left,
    y: event.clientY - componentRect.top
  }
  
  const handleMouseMove = (moveEvent) => {
    const newX = moveEvent.clientX - rect.left - dragOffset.value.x
    const newY = moveEvent.clientY - rect.top - dragOffset.value.y
    
    editorStore.updateComponentStyle(componentId, {
      left: Math.max(0, newX) + 'px',
      top: Math.max(0, newY) + 'px'
    })
  }
  
  const handleMouseUp = () => {
    isDraggingComponent.value = false
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }
  
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}
</script>

<style scoped lang="scss">
.canvas-editor {
  width: 100%;
  height: 100%;
  background: #f8f9fa;
  position: relative;
  overflow: auto;
  
  .canvas-content {
    min-height: 100%;
    position: relative;
    background: #fff;
    margin: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  .component-wrapper {
    position: absolute;
    cursor: move;
    
    &.selected {
      outline: 2px solid #409eff;
      outline-offset: 2px;
    }
    
    &:hover:not(.selected) {
      outline: 1px dashed #409eff;
      outline-offset: 1px;
    }
    
    .component-actions {
      position: absolute;
      top: -35px;
      right: -5px;
      z-index: 10;
    }
  }
  
  .drop-placeholder {
    border: 2px dashed #409eff;
    background: rgba(64, 158, 255, 0.1);
    border-radius: 4px;
    pointer-events: none;
  }
}
</style>
