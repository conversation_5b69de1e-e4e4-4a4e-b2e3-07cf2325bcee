<template>
  <el-dialog
    v-model="visible"
    title="导出代码"
    width="80%"
    :before-close="handleClose"
  >
    <div class="export-dialog">
      <!-- 导出类型选择 -->
      <div class="export-types">
        <el-radio-group v-model="exportType" @change="generateCode">
          <el-radio-button label="vue">Vue组件</el-radio-button>
          <el-radio-button label="html">HTML文件</el-radio-button>
          <el-radio-button label="json">项目配置</el-radio-button>
        </el-radio-group>
      </div>
      
      <!-- 代码预览区域 -->
      <div class="code-preview">
        <div class="preview-header">
          <span class="file-name">{{ fileName }}</span>
          <div class="actions">
            <el-button size="small" @click="copyCode">
              <el-icon><DocumentCopy /></el-icon>
              复制代码
            </el-button>
            <el-button size="small" type="primary" @click="downloadFile">
              <el-icon><Download /></el-icon>
              下载文件
            </el-button>
          </div>
        </div>
        <div class="code-content">
          <pre><code>{{ generatedCode }}</code></pre>
        </div>
      </div>
      
      <!-- 使用说明 -->
      <div class="usage-tips">
        <el-collapse>
          <el-collapse-item title="使用说明" name="usage">
            <div v-if="exportType === 'vue'">
              <h4>Vue组件使用方法：</h4>
              <ol>
                <li>将代码保存为 .vue 文件</li>
                <li>确保项目已安装 Element Plus</li>
                <li>在需要的地方导入并使用该组件</li>
              </ol>
            </div>
            <div v-else-if="exportType === 'html'">
              <h4>HTML文件使用方法：</h4>
              <ol>
                <li>将代码保存为 .html 文件</li>
                <li>直接在浏览器中打开即可预览</li>
                <li>已包含Vue和Element Plus的CDN链接</li>
              </ol>
            </div>
            <div v-else-if="exportType === 'json'">
              <h4>项目配置使用方法：</h4>
              <ol>
                <li>保存为 .json 文件作为项目备份</li>
                <li>可以通过导入功能重新加载项目</li>
                <li>包含完整的组件配置信息</li>
              </ol>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { DocumentCopy, Download } from '@element-plus/icons-vue'
import { CodeGenerator } from '@/utils/codeGenerator'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  components: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue'])

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const exportType = ref('vue')
const generatedCode = ref('')

// 文件名
const fileName = computed(() => {
  const timestamp = new Date().toISOString().slice(0, 10)
  const extensions = {
    vue: '.vue',
    html: '.html',
    json: '.json'
  }
  return `generated-page-${timestamp}${extensions[exportType.value]}`
})

// 生成代码
const generateCode = () => {
  if (!props.components || props.components.length === 0) {
    generatedCode.value = '// 暂无组件，请先在画布中添加组件'
    return
  }
  
  const generator = new CodeGenerator(props.components)
  
  switch (exportType.value) {
    case 'vue':
      generatedCode.value = generator.generateVueComponent()
      break
    case 'html':
      generatedCode.value = generator.generateHTML()
      break
    case 'json':
      generatedCode.value = generator.generateProjectJSON()
      break
    default:
      generatedCode.value = ''
  }
}

// 复制代码
const copyCode = async () => {
  try {
    await navigator.clipboard.writeText(generatedCode.value)
    ElMessage.success('代码已复制到剪贴板')
  } catch (error) {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = generatedCode.value
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    ElMessage.success('代码已复制到剪贴板')
  }
}

// 下载文件
const downloadFile = () => {
  const blob = new Blob([generatedCode.value], { 
    type: exportType.value === 'json' ? 'application/json' : 'text/plain' 
  })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = fileName.value
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
  ElMessage.success('文件下载成功')
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
}

// 监听组件变化，重新生成代码
watch(() => props.components, generateCode, { deep: true })
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    generateCode()
  }
})
</script>

<style scoped lang="scss">
.export-dialog {
  .export-types {
    margin-bottom: 20px;
    text-align: center;
  }
  
  .code-preview {
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: 20px;
    
    .preview-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      background: #f8f9fa;
      border-bottom: 1px solid #e0e0e0;
      
      .file-name {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 14px;
        color: #333;
      }
      
      .actions {
        display: flex;
        gap: 8px;
      }
    }
    
    .code-content {
      max-height: 400px;
      overflow: auto;
      background: #fafafa;
      
      pre {
        margin: 0;
        padding: 16px;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 13px;
        line-height: 1.5;
        color: #333;
        white-space: pre-wrap;
        word-wrap: break-word;
      }
    }
  }
  
  .usage-tips {
    h4 {
      margin: 0 0 12px 0;
      color: #333;
      font-size: 14px;
    }
    
    ol {
      margin: 0;
      padding-left: 20px;
      
      li {
        margin-bottom: 8px;
        font-size: 14px;
        color: #666;
      }
    }
  }
}

:deep(.el-dialog) {
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

:deep(.el-dialog__body) {
  flex: 1;
  overflow: auto;
}
</style>
