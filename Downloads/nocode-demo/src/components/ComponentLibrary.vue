<template>
  <div class="component-library">
    <div class="library-header">
      <h3>组件库</h3>
    </div>
    
    <div class="component-groups">
      <div class="component-group">
        <h4>基础组件</h4>
        <div class="component-list">
          <div
            v-for="component in basicComponents"
            :key="component.type"
            class="component-item"
            draggable="true"
            @dragstart="handleDragStart($event, component)"
          >
            <div class="component-icon">
              <component :is="component.icon" />
            </div>
            <span class="component-name">{{ component.name }}</span>
          </div>
        </div>
      </div>
      
      <div class="component-group">
        <h4>表单组件</h4>
        <div class="component-list">
          <div
            v-for="component in formComponents"
            :key="component.type"
            class="component-item"
            draggable="true"
            @dragstart="handleDragStart($event, component)"
          >
            <div class="component-icon">
              <component :is="component.icon" />
            </div>
            <span class="component-name">{{ component.name }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 基础组件配置
const basicComponents = ref([
  {
    type: 'text',
    name: '文本',
    icon: 'Document'
  },
  {
    type: 'image',
    name: '图片',
    icon: 'Picture'
  }
])

// 表单组件配置
const formComponents = ref([
  {
    type: 'input',
    name: '输入框',
    icon: 'Edit'
  },
  {
    type: 'button',
    name: '按钮',
    icon: 'Pointer'
  }
])

// 处理拖拽开始
const handleDragStart = (event, component) => {
  event.dataTransfer.setData('component-type', component.type)
  event.dataTransfer.setData('component-name', component.name)
  event.dataTransfer.effectAllowed = 'copy'
}
</script>

<style scoped lang="scss">
.component-library {
  height: 100%;
  padding: 16px;
  
  .library-header {
    margin-bottom: 20px;
    
    h3 {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }
  }
  
  .component-groups {
    .component-group {
      margin-bottom: 24px;
      
      h4 {
        font-size: 14px;
        color: #666;
        margin-bottom: 12px;
        padding-bottom: 8px;
        border-bottom: 1px solid #e0e0e0;
      }
      
      .component-list {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 8px;
        
        .component-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 12px 8px;
          border: 1px solid #e0e0e0;
          border-radius: 6px;
          cursor: grab;
          transition: all 0.2s;
          background: #fff;
          
          &:hover {
            border-color: #409eff;
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
          }
          
          &:active {
            cursor: grabbing;
          }
          
          .component-icon {
            font-size: 20px;
            color: #409eff;
            margin-bottom: 6px;
          }
          
          .component-name {
            font-size: 12px;
            color: #333;
            text-align: center;
          }
        }
      }
    }
  }
}
</style>
