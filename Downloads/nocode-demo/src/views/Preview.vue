<template>
  <div class="preview-view">
    <!-- 预览工具栏 -->
    <div class="preview-toolbar">
      <div class="toolbar-left">
        <el-button @click="goBack">
          <el-icon><ArrowLeft /></el-icon>
          返回编辑
        </el-button>
      </div>
      <div class="toolbar-center">
        <h3>页面预览</h3>
      </div>
      <div class="toolbar-right">
        <el-button @click="refreshPreview">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>
    
    <!-- 预览内容区域 -->
    <div class="preview-container">
      <div class="preview-canvas" ref="previewCanvasRef">
        <!-- 渲染所有组件 -->
        <div
          v-for="component in components"
          :key="component.id"
          :style="component.style"
          class="preview-component"
        >
          <component
            :is="getComponent(component.type)"
            v-bind="component.props"
            @click="handleComponentClick(component)"
          />
        </div>
        
        <!-- 空状态 -->
        <div v-if="components.length === 0" class="empty-state">
          <el-empty description="暂无内容，请先在编辑器中添加组件" />
          <el-button type="primary" @click="goBack">
            去编辑
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Refresh } from '@element-plus/icons-vue'
import { getComponent } from '@/components/RenderComponents'

const router = useRouter()
const previewCanvasRef = ref(null)

// 组件数据
const components = ref([])

// 返回编辑器
const goBack = () => {
  router.push('/editor')
}

// 刷新预览
const refreshPreview = () => {
  loadPreviewData()
  ElMessage.success('预览已刷新')
}

// 加载预览数据
const loadPreviewData = () => {
  try {
    const savedProject = localStorage.getItem('nocode-project')
    if (savedProject) {
      const projectData = JSON.parse(savedProject)
      if (projectData.components && Array.isArray(projectData.components)) {
        components.value = projectData.components
      }
    }
  } catch (error) {
    console.error('加载预览数据失败:', error)
    ElMessage.error('加载预览数据失败')
  }
}

// 处理组件点击事件
const handleComponentClick = (component) => {
  // 在预览模式下处理组件交互
  if (component.type === 'button') {
    // 按钮点击事件
    if (component.props.text === '登录' || component.props.text === '提交') {
      handleLogin()
    } else {
      ElMessage.info(`点击了按钮: ${component.props.text}`)
    }
  }
}

// 模拟登录处理
const handleLogin = () => {
  // 获取输入框的值（简单模拟）
  const inputs = components.value.filter(comp => comp.type === 'input')
  const username = inputs.find(input => 
    input.props.placeholder?.includes('用户名') || 
    input.props.placeholder?.includes('账号')
  )
  const password = inputs.find(input => 
    input.props.type === 'password' ||
    input.props.placeholder?.includes('密码')
  )
  
  if (username && password) {
    ElMessage.success('登录功能演示：登录成功！')
  } else {
    ElMessage.warning('请确保页面包含用户名和密码输入框')
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadPreviewData()
})
</script>

<style scoped lang="scss">
.preview-view {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f0f2f5;
  
  .preview-toolbar {
    height: 60px;
    background: #fff;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    
    .toolbar-center {
      flex: 1;
      display: flex;
      justify-content: center;
      
      h3 {
        margin: 0;
        font-size: 16px;
        color: #333;
      }
    }
  }
  
  .preview-container {
    flex: 1;
    overflow: auto;
    padding: 20px;
    
    .preview-canvas {
      max-width: 1200px;
      margin: 0 auto;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      min-height: 600px;
      position: relative;
      
      .preview-component {
        position: absolute;
        
        // 在预览模式下移除编辑相关的样式
        &:hover {
          outline: none;
        }
      }
      
      .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 400px;
        gap: 20px;
      }
    }
  }
}

// 全局样式，确保预览模式下的交互性
:deep(.preview-component) {
  .el-input {
    pointer-events: auto;
  }

  .el-button {
    pointer-events: auto;
    cursor: pointer;
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .preview-view {
    .preview-toolbar {
      height: auto;
      min-height: 60px;
      padding: 10px;
      flex-wrap: wrap;
      gap: 10px;

      .toolbar-center {
        h3 {
          font-size: 14px;
        }
      }

      .el-button {
        font-size: 12px;
        padding: 8px 12px;
      }
    }

    .preview-container {
      padding: 10px;

      .preview-canvas {
        min-height: 400px;

        .preview-component {
          // 移动端组件优化
          .el-input {
            font-size: 16px; // 防止iOS缩放
          }

          .el-button {
            min-height: 44px; // 符合移动端触摸标准
            font-size: 16px;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .preview-view {
    .preview-toolbar {
      .el-button {
        font-size: 11px;
        padding: 6px 8px;
      }
    }

    .preview-container {
      padding: 5px;

      .preview-canvas {
        min-height: 300px;
        border-radius: 4px;
      }
    }
  }
}
</style>
