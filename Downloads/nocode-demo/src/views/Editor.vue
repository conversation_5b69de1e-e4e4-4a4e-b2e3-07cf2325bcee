<template>
  <div class="editor-view">
    <!-- 顶部工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <h2>Vue 低码平台</h2>
      </div>
      <div class="toolbar-center">
        <el-button-group>
          <el-button
            :type="mode === 'edit' ? 'primary' : 'default'"
            @click="setMode('edit')"
          >
            编辑模式
          </el-button>
          <el-button
            :type="mode === 'preview' ? 'primary' : 'default'"
            @click="setMode('preview')"
          >
            预览模式
          </el-button>
        </el-button-group>

        <!-- 设备预览切换 -->
        <div class="device-selector" v-if="mode === 'preview'">
          <el-button-group>
            <el-button
              v-for="(config, device) in deviceSizes"
              :key="device"
              :type="previewDevice === device ? 'primary' : 'default'"
              :icon="getDeviceIcon(device)"
              @click="setPreviewDevice(device)"
            >
              {{ config.label }}
            </el-button>
          </el-button-group>
        </div>
      </div>
      <div class="toolbar-right">
        <el-dropdown @command="handleTemplateCommand">
          <el-button>
            模板 <el-icon><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="login">登录页面模板</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-button @click="showImportDialog">导入项目</el-button>
        <el-button @click="clearCanvas">清空画布</el-button>
        <el-button @click="saveProject">保存项目</el-button>
        <el-button @click="showExportDialog">导出代码</el-button>
        <el-button type="primary" @click="goToPreview">在线预览</el-button>
      </div>
    </div>
    
    <!-- 主要内容区域 -->
    <div class="editor-container">
      <!-- 左侧组件面板 -->
      <div v-if="mode === 'edit'" class="component-panel">
        <ComponentLibrary />
      </div>
      
      <!-- 中间画布区域 -->
      <div class="canvas-container">
        <div
          class="canvas-wrapper"
          :class="{ 'device-preview': mode === 'preview' && previewDevice !== 'desktop' }"
        >
          <div
            class="canvas-frame"
            :style="canvasFrameStyle"
          >
            <CanvasEditor :preview-mode="mode === 'preview'" />
          </div>
        </div>
      </div>
      
      <!-- 右侧属性面板 -->
      <div v-if="mode === 'edit'" class="property-panel">
        <PropertyPanel />
      </div>
    </div>

    <!-- 导出对话框 -->
    <ExportDialog
      v-model="exportDialogVisible"
      :components="editorStore.components"
    />

    <!-- 导入对话框 -->
    <ImportDialog
      v-model="importDialogVisible"
      @import="handleImport"
    />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowDown, Monitor, Iphone, Ipad } from '@element-plus/icons-vue'
import { useEditorStore } from '@/stores/editor'
import ComponentLibrary from '@/components/ComponentLibrary.vue'
import CanvasEditor from '@/components/CanvasEditor.vue'
import PropertyPanel from '@/components/PropertyPanel.vue'
import ExportDialog from '@/components/ExportDialog.vue'
import ImportDialog from '@/components/ImportDialog.vue'

const router = useRouter()
const editorStore = useEditorStore()

// 当前模式：edit 或 preview
const mode = ref('edit')

// 导出对话框显示状态
const exportDialogVisible = ref(false)

// 导入对话框显示状态
const importDialogVisible = ref(false)

// 从store中获取设备相关数据
const previewDevice = computed(() => editorStore.previewDevice)
const deviceSizes = computed(() => editorStore.deviceSizes)

// 画布框架样式
const canvasFrameStyle = computed(() => {
  if (mode.value === 'preview' && previewDevice.value !== 'desktop') {
    const config = deviceSizes.value[previewDevice.value]
    return {
      width: config.width,
      height: config.height,
      margin: '20px auto',
      border: '1px solid #ddd',
      borderRadius: '8px',
      overflow: 'hidden',
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
    }
  }
  return {
    width: '100%',
    height: '100%'
  }
})

// 设置模式
const setMode = (newMode) => {
  mode.value = newMode
  if (newMode === 'preview') {
    editorStore.clearSelection()
  }
}

// 设置预览设备
const setPreviewDevice = (device) => {
  editorStore.setPreviewDevice(device)
}

// 获取设备图标
const getDeviceIcon = (device) => {
  const iconMap = {
    desktop: Monitor,
    tablet: Ipad,
    mobile: Iphone
  }
  return iconMap[device]
}

// 清空画布
const clearCanvas = () => {
  editorStore.clearCanvas()
  ElMessage.success('画布已清空')
}

// 保存项目
const saveProject = () => {
  const projectData = {
    components: editorStore.components,
    timestamp: Date.now()
  }
  
  // 保存到本地存储
  localStorage.setItem('nocode-project', JSON.stringify(projectData))
  ElMessage.success('项目已保存到本地')
}

// 处理模板命令
const handleTemplateCommand = (command) => {
  if (command === 'login') {
    editorStore.loadLoginTemplate()
    ElMessage.success('登录页面模板已加载')
  }
}

// 显示导出对话框
const showExportDialog = () => {
  if (editorStore.components.length === 0) {
    ElMessage.warning('请先添加组件再导出')
    return
  }
  exportDialogVisible.value = true
}

// 显示导入对话框
const showImportDialog = () => {
  importDialogVisible.value = true
}

// 处理导入
const handleImport = ({ data, replaceExisting }) => {
  if (replaceExisting) {
    // 替换现有项目
    editorStore.clearCanvas()
  }

  // 导入组件数据
  if (data.components && Array.isArray(data.components)) {
    data.components.forEach(component => {
      // 重新生成ID以避免冲突
      const newComponent = {
        ...component,
        id: `component_${editorStore.componentIdCounter++}`
      }
      editorStore.components.push(newComponent)
    })
  }

  // 清空选择
  editorStore.clearSelection()
}

// 跳转到预览页面
const goToPreview = () => {
  // 先保存项目
  saveProject()
  // 跳转到预览页面
  router.push('/preview')
}

// 页面加载时尝试恢复项目
const loadProject = () => {
  try {
    const savedProject = localStorage.getItem('nocode-project')
    if (savedProject) {
      const projectData = JSON.parse(savedProject)
      if (projectData.components && Array.isArray(projectData.components)) {
        // 恢复组件数据
        editorStore.components.splice(0, editorStore.components.length, ...projectData.components)
        ElMessage.success('已恢复上次保存的项目')
      }
    }
  } catch (error) {
    console.error('加载项目失败:', error)
  }
}

// 组件挂载时加载项目
loadProject()
</script>

<style scoped lang="scss">
.editor-view {
  height: 100vh;
  display: flex;
  flex-direction: column;
  
  .toolbar {
    height: 60px;
    background: #fff;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    
    .toolbar-left {
      h2 {
        margin: 0;
        font-size: 18px;
        color: #333;
      }
    }
    
    .toolbar-center {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 20px;

      .device-selector {
        margin-left: 20px;
      }
    }
    
    .toolbar-right {
      display: flex;
      gap: 12px;
      flex-wrap: wrap;
    }
  }
  
  .editor-container {
    flex: 1;
    display: flex;
    overflow: hidden;
    
    .component-panel {
      width: 250px;
      background: #f5f5f5;
      border-right: 1px solid #e0e0e0;
      overflow-y: auto;
    }
    
    .canvas-container {
      flex: 1;
      background: #f8f9fa;
      position: relative;
      overflow: auto;

      .canvas-wrapper {
        width: 100%;
        height: 100%;

        &.device-preview {
          display: flex;
          align-items: flex-start;
          justify-content: center;
          padding: 20px;
          background: #e8e8e8;
        }

        .canvas-frame {
          background: #fff;
          transition: all 0.3s ease;
        }
      }
    }
    
    .property-panel {
      width: 300px;
      background: #f5f5f5;
      border-left: 1px solid #e0e0e0;
      overflow-y: auto;
    }
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .editor-view {
    .toolbar {
      height: auto;
      min-height: 60px;
      padding: 10px;
      flex-wrap: wrap;
      gap: 10px;

      .toolbar-left {
        h2 {
          font-size: 16px;
        }
      }

      .toolbar-center {
        flex-direction: column;
        gap: 10px;

        .device-selector {
          margin-left: 0;
        }
      }

      .toolbar-right {
        gap: 8px;

        .el-button {
          font-size: 12px;
          padding: 8px 12px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .editor-view {
    .toolbar {
      .toolbar-center {
        .el-button-group {
          .el-button {
            font-size: 11px;
            padding: 6px 8px;
          }
        }
      }

      .toolbar-right {
        .el-button {
          font-size: 11px;
          padding: 6px 8px;
        }
      }
    }
  }
}
</style>
