<template>
  <div class="editor-view">
    <!-- 顶部工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <h2>Vue 低码平台</h2>
      </div>
      <div class="toolbar-center">
        <el-button-group>
          <el-button
            :type="mode === 'edit' ? 'primary' : 'default'"
            @click="setMode('edit')"
          >
            编辑模式
          </el-button>
          <el-button
            :type="mode === 'preview' ? 'primary' : 'default'"
            @click="setMode('preview')"
          >
            预览模式
          </el-button>
        </el-button-group>
      </div>
      <div class="toolbar-right">
        <el-dropdown @command="handleTemplateCommand">
          <el-button>
            模板 <el-icon><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="login">登录页面模板</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-button @click="clearCanvas">清空画布</el-button>
        <el-button @click="saveProject">保存项目</el-button>
        <el-button type="primary" @click="goToPreview">在线预览</el-button>
      </div>
    </div>
    
    <!-- 主要内容区域 -->
    <div class="editor-container">
      <!-- 左侧组件面板 -->
      <div v-if="mode === 'edit'" class="component-panel">
        <ComponentLibrary />
      </div>
      
      <!-- 中间画布区域 -->
      <div class="canvas-container">
        <CanvasEditor :preview-mode="mode === 'preview'" />
      </div>
      
      <!-- 右侧属性面板 -->
      <div v-if="mode === 'edit'" class="property-panel">
        <PropertyPanel />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowDown } from '@element-plus/icons-vue'
import { useEditorStore } from '@/stores/editor'
import ComponentLibrary from '@/components/ComponentLibrary.vue'
import CanvasEditor from '@/components/CanvasEditor.vue'
import PropertyPanel from '@/components/PropertyPanel.vue'

const router = useRouter()
const editorStore = useEditorStore()

// 当前模式：edit 或 preview
const mode = ref('edit')

// 设置模式
const setMode = (newMode) => {
  mode.value = newMode
  if (newMode === 'preview') {
    editorStore.clearSelection()
  }
}

// 清空画布
const clearCanvas = () => {
  editorStore.clearCanvas()
  ElMessage.success('画布已清空')
}

// 保存项目
const saveProject = () => {
  const projectData = {
    components: editorStore.components,
    timestamp: Date.now()
  }
  
  // 保存到本地存储
  localStorage.setItem('nocode-project', JSON.stringify(projectData))
  ElMessage.success('项目已保存到本地')
}

// 处理模板命令
const handleTemplateCommand = (command) => {
  if (command === 'login') {
    editorStore.loadLoginTemplate()
    ElMessage.success('登录页面模板已加载')
  }
}

// 跳转到预览页面
const goToPreview = () => {
  // 先保存项目
  saveProject()
  // 跳转到预览页面
  router.push('/preview')
}

// 页面加载时尝试恢复项目
const loadProject = () => {
  try {
    const savedProject = localStorage.getItem('nocode-project')
    if (savedProject) {
      const projectData = JSON.parse(savedProject)
      if (projectData.components && Array.isArray(projectData.components)) {
        // 恢复组件数据
        editorStore.components.splice(0, editorStore.components.length, ...projectData.components)
        ElMessage.success('已恢复上次保存的项目')
      }
    }
  } catch (error) {
    console.error('加载项目失败:', error)
  }
}

// 组件挂载时加载项目
loadProject()
</script>

<style scoped lang="scss">
.editor-view {
  height: 100vh;
  display: flex;
  flex-direction: column;
  
  .toolbar {
    height: 60px;
    background: #fff;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    
    .toolbar-left {
      h2 {
        margin: 0;
        font-size: 18px;
        color: #333;
      }
    }
    
    .toolbar-center {
      flex: 1;
      display: flex;
      justify-content: center;
    }
    
    .toolbar-right {
      display: flex;
      gap: 12px;
    }
  }
  
  .editor-container {
    flex: 1;
    display: flex;
    overflow: hidden;
    
    .component-panel {
      width: 250px;
      background: #f5f5f5;
      border-right: 1px solid #e0e0e0;
      overflow-y: auto;
    }
    
    .canvas-container {
      flex: 1;
      background: #f8f9fa;
      position: relative;
      overflow: auto;
    }
    
    .property-panel {
      width: 300px;
      background: #f5f5f5;
      border-left: 1px solid #e0e0e0;
      overflow-y: auto;
    }
  }
}
</style>
