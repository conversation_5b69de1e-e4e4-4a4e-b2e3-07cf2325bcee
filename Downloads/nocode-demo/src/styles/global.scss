* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.editor-container {
  display: flex;
  height: 100vh;
  
  .component-panel {
    width: 250px;
    background: #f5f5f5;
    border-right: 1px solid #e0e0e0;
    overflow-y: auto;
  }
  
  .canvas-container {
    flex: 1;
    background: #fff;
    position: relative;
    overflow: auto;
  }
  
  .property-panel {
    width: 300px;
    background: #f5f5f5;
    border-left: 1px solid #e0e0e0;
    overflow-y: auto;
  }
}

.canvas {
  min-height: 100%;
  position: relative;
  background: #fff;
  
  &.preview-mode {
    padding: 20px;
  }
}

.component-item {
  position: relative;
  cursor: pointer;
  
  &.selected {
    outline: 2px solid #409eff;
    outline-offset: 2px;
  }
  
  &:hover {
    outline: 1px dashed #409eff;
    outline-offset: 1px;
  }
}

.drag-placeholder {
  border: 2px dashed #409eff;
  background: rgba(64, 158, 255, 0.1);
  min-height: 40px;
  margin: 5px 0;
}
