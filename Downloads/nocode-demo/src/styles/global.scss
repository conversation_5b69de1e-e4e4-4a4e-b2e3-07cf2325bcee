* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>,
    "Helvetica Neue", Arial, sans-serif;
}

.editor-container {
  display: flex;
  height: 100vh;

  .component-panel {
    width: 250px;
    background: #f5f5f5;
    border-right: 1px solid #e0e0e0;
    overflow-y: auto;
  }

  .canvas-container {
    flex: 1;
    background: #fff;
    position: relative;
    overflow: auto;
  }

  .property-panel {
    width: 300px;
    background: #f5f5f5;
    border-left: 1px solid #e0e0e0;
    overflow-y: auto;
  }
}

.canvas {
  min-height: 100%;
  position: relative;
  background: #fff;

  &.preview-mode {
    padding: 20px;
  }
}

.component-item {
  position: relative;
  cursor: pointer;

  &.selected {
    outline: 2px solid #409eff;
    outline-offset: 2px;
  }

  &:hover {
    outline: 1px dashed #409eff;
    outline-offset: 1px;
  }
}

.drag-placeholder {
  border: 2px dashed #409eff;
  background: rgba(64, 158, 255, 0.1);
  min-height: 40px;
  margin: 5px 0;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .editor-container {
    flex-direction: column;

    .component-panel {
      width: 100%;
      height: 200px;
      border-right: none;
      border-bottom: 1px solid #e0e0e0;
      overflow-x: auto;
      overflow-y: hidden;

      .component-library {
        display: flex;
        flex-direction: row;
        height: 100%;

        .component-groups {
          display: flex;
          gap: 20px;
          padding: 16px;

          .component-group {
            min-width: 200px;

            .component-list {
              display: flex;
              gap: 8px;
            }
          }
        }
      }
    }

    .property-panel {
      width: 100%;
      height: 300px;
      border-left: none;
      border-top: 1px solid #e0e0e0;
    }
  }

  .canvas {
    padding: 10px;
  }

  .component-item {
    &.selected {
      outline-width: 3px;
    }

    &:hover {
      outline-width: 2px;
    }
  }
}

@media (max-width: 480px) {
  .editor-container {
    .component-panel {
      height: 150px;

      .component-library {
        .component-groups {
          padding: 8px;

          .component-group {
            min-width: 150px;

            .component-list {
              .component-item {
                padding: 8px 4px;

                .component-icon {
                  font-size: 16px;
                }

                .component-name {
                  font-size: 11px;
                }
              }
            }
          }
        }
      }
    }

    .property-panel {
      height: 250px;
    }
  }

  .canvas {
    padding: 5px;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .component-item {
    &:hover {
      outline: none;
    }

    &:active {
      outline: 2px solid #409eff;
      outline-offset: 2px;
    }
  }

  .component-wrapper {
    &:hover:not(.selected) {
      outline: none;
    }

    &:active:not(.selected) {
      outline: 1px dashed #409eff;
      outline-offset: 1px;
    }
  }
}
