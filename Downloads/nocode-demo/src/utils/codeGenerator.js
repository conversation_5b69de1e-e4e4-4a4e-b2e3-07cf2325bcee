// 代码生成工具类
export class CodeGenerator {
  constructor(components) {
    this.components = components
  }

  // 生成Vue单文件组件
  generateVueComponent() {
    const template = this.generateTemplate()
    const script = this.generateScript()
    const style = this.generateStyle()

    return `<template>
${template}
</template>

<script setup>
${script}
</script>

<style scoped>
${style}
</style>`
  }

  // 生成HTML文件
  generateHTML() {
    const template = this.generateTemplate()
    const style = this.generateInlineStyle()
    const script = this.generateInlineScript()

    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Generated Page</title>
  <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
  <style>
${style}
  </style>
</head>
<body>
  <div id="app">
${template}
  </div>
  
  <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
  <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
  <script>
${script}
  </script>
</body>
</html>`
  }

  // 生成模板部分
  generateTemplate() {
    let template = '  <div class="page-container">\n'
    
    this.components.forEach(component => {
      template += this.generateComponentTemplate(component)
    })
    
    template += '  </div>'
    return template
  }

  // 生成单个组件模板
  generateComponentTemplate(component) {
    const { type, props, style } = component
    const styleStr = this.styleObjectToString(style)
    
    switch (type) {
      case 'text':
        return `    <${props.tag || 'div'} class="component-${component.id}" style="${styleStr}">${props.content || '文本内容'}</${props.tag || 'div'}>\n`
      
      case 'input':
        return `    <el-input 
      class="component-${component.id}" 
      style="${styleStr}"
      placeholder="${props.placeholder || ''}"
      type="${props.type || 'text'}"
      ${props.disabled ? 'disabled' : ''}
      size="${props.size || 'default'}"
    />\n`
      
      case 'button':
        return `    <el-button 
      class="component-${component.id}" 
      style="${styleStr}"
      type="${props.type || 'primary'}"
      size="${props.size || 'default'}"
      ${props.disabled ? 'disabled' : ''}
      ${props.loading ? 'loading' : ''}
      ${props.plain ? 'plain' : ''}
      ${props.round ? 'round' : ''}
      ${props.circle ? 'circle' : ''}
      @click="handleButtonClick('${component.id}')"
    >${props.text || '按钮'}</el-button>\n`
      
      case 'image':
        if (props.src) {
          return `    <img 
      class="component-${component.id}" 
      style="${styleStr}"
      src="${props.src}"
      alt="${props.alt || '图片'}"
    />\n`
        } else {
          return `    <div class="component-${component.id} image-placeholder" style="${styleStr}">
      <span>图片占位符</span>
    </div>\n`
        }
      
      default:
        return `    <div class="component-${component.id}" style="${styleStr}">未知组件</div>\n`
    }
  }

  // 生成脚本部分
  generateScript() {
    return `import { ref } from 'vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const formData = ref({})

// 按钮点击处理
const handleButtonClick = (componentId) => {
  console.log('Button clicked:', componentId)
  
  // 简单的登录逻辑示例
  if (componentId.includes('login') || componentId.includes('submit')) {
    ElMessage.success('操作成功！')
  } else {
    ElMessage.info('按钮被点击')
  }
}`
  }

  // 生成样式部分
  generateStyle() {
    let styles = `.page-container {
  position: relative;
  min-height: 100vh;
  background: #fff;
}

.image-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border: 1px dashed #ccc;
  color: #999;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-container {
    padding: 10px;
  }
  
  .el-button {
    min-height: 44px;
    font-size: 16px;
  }
  
  .el-input {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .page-container {
    padding: 5px;
  }
}
`

    // 添加组件特定样式
    this.components.forEach(component => {
      if (component.style.position === 'absolute') {
        styles += `
.component-${component.id} {
  position: absolute;
  left: ${component.style.left || '0px'};
  top: ${component.style.top || '0px'};
}
`
      }
    })

    return styles
  }

  // 生成内联样式（用于HTML）
  generateInlineStyle() {
    return this.generateStyle().replace(/\n/g, '\n    ')
  }

  // 生成内联脚本（用于HTML）
  generateInlineScript() {
    return `const { createApp, ref } = Vue
const { ElMessage } = ElementPlus

createApp({
  setup() {
    const formData = ref({})
    
    const handleButtonClick = (componentId) => {
      console.log('Button clicked:', componentId)
      
      if (componentId.includes('login') || componentId.includes('submit')) {
        ElMessage.success('操作成功！')
      } else {
        ElMessage.info('按钮被点击')
      }
    }
    
    return {
      formData,
      handleButtonClick
    }
  }
}).use(ElementPlus).mount('#app')`
  }

  // 样式对象转字符串
  styleObjectToString(styleObj) {
    return Object.entries(styleObj)
      .map(([key, value]) => {
        // 转换驼峰命名为短横线命名
        const cssKey = key.replace(/([A-Z])/g, '-$1').toLowerCase()
        return `${cssKey}: ${value}`
      })
      .join('; ')
  }

  // 生成项目配置JSON
  generateProjectJSON() {
    return JSON.stringify({
      version: '1.0.0',
      components: this.components,
      metadata: {
        createdAt: new Date().toISOString(),
        generator: 'Vue NoCode Platform'
      }
    }, null, 2)
  }
}
