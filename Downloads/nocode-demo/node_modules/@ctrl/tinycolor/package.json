{"name": "@ctrl/tinycolor", "version": "3.6.1", "description": "Fast, small color manipulation and conversion for JavaScript", "author": "<PERSON> <<EMAIL>>", "publishConfig": {"access": "public"}, "license": "MIT", "homepage": "https://tinycolor.vercel.app", "repository": "scttcper/tinycolor", "keywords": ["typescript", "color", "manipulation", "tinycolor", "hsa", "rgb"], "main": "dist/public_api.js", "module": "dist/module/public_api.js", "typings": "dist/public_api.d.ts", "files": ["dist"], "sideEffects": false, "scripts": {"build:demo": "rollup -c rollup.demo.js", "watch:demo": "rollup -c rollup.demo.js -w", "lint": "eslint --ext .js,.ts, .", "lint:fix": "eslint --fix --ext .js,.ts, .", "prepare": "npm run build", "build": "del-cli dist && tsc -p tsconfig.build.json && tsc -p tsconfig.module.json && ts-node build", "build:docs": "typedoc --out demo/public/docs --hideGenerator --tsconfig tsconfig.build.json src/public_api.ts", "test": "jest", "test:ci": "jest --ci --runInBand --reporters=default --reporters=jest-junit --coverage", "test:watch": "jest --watch"}, "dependencies": {}, "devDependencies": {"@babel/plugin-transform-modules-commonjs": "7.19.6", "@babel/preset-typescript": "7.18.6", "@ctrl/eslint-config": "3.5.6", "@jest/globals": "29.3.1", "@types/node": "18.11.11", "del-cli": "5.0.0", "jest": "29.3.1", "jest-junit": "15.0.0", "rollup": "2.70.1", "rollup-plugin-livereload": "2.0.5", "rollup-plugin-serve": "1.1.0", "rollup-plugin-sourcemaps": "0.6.3", "rollup-plugin-terser": "7.0.2", "rollup-plugin-typescript2": "0.34.1", "ts-node": "10.9.1", "typedoc": "0.23.21", "typescript": "4.9.3"}, "jest": {"testEnvironment": "node", "coverageProvider": "v8", "moduleNameMapper": {"(.+)\\.js": "$1"}}, "babel": {"presets": ["@babel/preset-typescript"], "plugins": ["@babel/plugin-transform-modules-commonjs"]}, "release": {"branch": "master"}, "engines": {"node": ">=10"}}