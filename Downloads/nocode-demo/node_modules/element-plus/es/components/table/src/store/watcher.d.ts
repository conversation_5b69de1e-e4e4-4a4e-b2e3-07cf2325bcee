import type { Ref } from 'vue';
import type { TableColumnCtx } from '../table-column/defaults';
import type { DefaultRow, TableSortOrder } from '../table/defaults';
import type { StoreFilter } from '.';
declare function useWatcher<T extends DefaultRow>(): {
    assertRowKey: () => void;
    updateColumns: () => void;
    scheduleLayout: (needUpdateColumns?: boolean, immediate?: boolean) => void;
    isSelected: (row: T) => boolean;
    clearSelection: () => void;
    cleanSelection: () => void;
    getSelectionRows: () => T[];
    toggleRowSelection: (row: T, selected?: boolean, emitChange?: boolean, ignoreSelectable?: boolean) => void;
    _toggleAllSelection: () => void;
    toggleAllSelection: (() => void) | null;
    updateAllSelected: () => void;
    updateFilters: (column: TableColumnCtx<T>, values: string[]) => Record<string, string[]>;
    updateCurrentRow: (_currentRow: T) => void;
    updateSort: (column: TableColumnCtx<T> | null, prop: string | null, order: TableSortOrder | null) => void;
    execFilter: () => void;
    execSort: () => void;
    execQuery: (ignore?: {
        filter: boolean;
    } | undefined) => void;
    clearFilter: (columnKeys?: string[] | string) => void;
    clearSort: () => void;
    toggleRowExpansion: (row: T, expanded?: boolean) => void;
    setExpandRowKeysAdapter: (val: string[]) => void;
    setCurrentRowKey: (key: string) => void;
    toggleRowExpansionAdapter: (row: T, expanded?: boolean) => void;
    isRowExpanded: (row: T) => boolean;
    updateExpandRows: () => void;
    updateCurrentRowData: () => void;
    loadOrToggle: (row: T) => void;
    updateTreeData: (ifChangeExpandRowKeys?: boolean, ifExpandAll?: boolean) => void;
    updateKeyChildren: (key: string, data: T[]) => void;
    states: {
        _currentRowKey: Ref<string | null>;
        currentRow: Ref<T | null>;
        expandRowKeys: Ref<string[]>;
        treeData: Ref<Record<string, import("./tree").TreeData>>;
        indent: Ref<number>;
        lazy: Ref<boolean>;
        lazyTreeNodeMap: Ref<Record<string, T[]>>;
        lazyColumnIdentifier: Ref<string>;
        childrenColumnName: Ref<string>;
        checkStrictly: Ref<boolean>;
        expandRows: Ref<T[]>;
        defaultExpandAll: Ref<boolean>;
        tableSize: Ref<any>;
        rowKey: Ref<string | null>;
        data: Ref<T[]>;
        _data: Ref<T[]>;
        isComplex: Ref<boolean>;
        _columns: Ref<TableColumnCtx<T>[]>;
        originColumns: Ref<TableColumnCtx<T>[]>;
        columns: Ref<TableColumnCtx<T>[]>;
        fixedColumns: Ref<TableColumnCtx<T>[]>;
        rightFixedColumns: Ref<TableColumnCtx<T>[]>;
        leafColumns: Ref<TableColumnCtx<T>[]>;
        fixedLeafColumns: Ref<TableColumnCtx<T>[]>;
        rightFixedLeafColumns: Ref<TableColumnCtx<T>[]>;
        updateOrderFns: (() => void)[];
        leafColumnsLength: Ref<number>;
        fixedLeafColumnsLength: Ref<number>;
        rightFixedLeafColumnsLength: Ref<number>;
        isAllSelected: Ref<boolean>;
        selection: Ref<T[]>;
        reserveSelection: Ref<boolean>;
        selectOnIndeterminate: Ref<boolean>;
        selectable: Ref<((row: T, index: number) => boolean) | null>;
        filters: Ref<StoreFilter>;
        filteredData: Ref<T[] | null>;
        sortingColumn: Ref<TableColumnCtx<T> | null>;
        sortProp: Ref<string | null>;
        sortOrder: Ref<string | number | null>;
        hoverRow: Ref<T | null>;
    };
};
export default useWatcher;
