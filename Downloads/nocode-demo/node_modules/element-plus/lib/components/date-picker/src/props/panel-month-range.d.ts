import type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue';
export declare const panelMonthRangeProps: {
    readonly unlinkPanels: BooleanConstructor;
    readonly visible: BooleanConstructor;
    readonly showFooter: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
    readonly parsedValue: {
        readonly type: import("vue").PropType<import("dayjs").Dayjs[]>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
};
export declare const panelMonthRangeEmits: string[];
export type PanelMonthRangeProps = ExtractPropTypes<typeof panelMonthRangeProps>;
export type PanelMonthRangePropsPublic = __ExtractPublicPropTypes<typeof panelMonthRangeProps>;
