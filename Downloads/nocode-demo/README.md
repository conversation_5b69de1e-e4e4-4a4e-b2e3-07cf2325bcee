# Vue 低码平台

一个基于 Vue 3 的简单低码平台，支持通过拖拽方式搭建登录页面并在线预览。

## 功能特性

- 🎨 **可视化编辑器**: 拖拽式组件编辑，所见即所得
- 🧩 **丰富组件库**: 包含文本、输入框、按钮、图片等基础组件
- ⚙️ **属性配置**: 实时编辑组件属性和样式
- 👀 **在线预览**: 支持预览模式和独立预览页面
- 📱 **响应式设计**: 适配不同屏幕尺寸
- 💾 **本地存储**: 自动保存项目到本地存储
- 🎯 **登录模板**: 内置登录页面模板，快速开始

## 技术栈

- **前端框架**: Vue 3 + Composition API
- **构建工具**: Vite
- **UI 组件库**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router
- **样式**: SCSS
- **拖拽**: 原生 HTML5 Drag & Drop API

## 项目结构

```
src/
├── components/
│   ├── ComponentLibrary.vue      # 组件库面板
│   ├── CanvasEditor.vue          # 画布编辑器
│   ├── PropertyPanel.vue         # 属性配置面板
│   ├── RenderComponents/         # 可渲染组件
│   │   ├── TextComponent.vue     # 文本组件
│   │   ├── InputComponent.vue    # 输入框组件
│   │   ├── ButtonComponent.vue   # 按钮组件
│   │   ├── ImageComponent.vue    # 图片组件
│   │   └── index.js             # 组件导出
│   └── PropertyEditors/          # 属性编辑器
│       ├── StyleEditor.vue       # 样式编辑器
│       ├── TextPropertyEditor.vue
│       ├── InputPropertyEditor.vue
│       ├── ButtonPropertyEditor.vue
│       └── ImagePropertyEditor.vue
├── stores/
│   └── editor.js                 # 编辑器状态管理
├── views/
│   ├── Editor.vue               # 编辑器主页面
│   └── Preview.vue              # 预览页面
├── router/
│   └── index.js                 # 路由配置
└── styles/
    └── global.scss              # 全局样式
```

## 快速开始

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 预览生产版本

```bash
npm run preview
```

## 使用说明

### 1. 编辑模式

- **组件库**: 左侧面板包含可拖拽的组件
- **画布**: 中间区域，拖拽组件到此处进行布局
- **属性面板**: 右侧面板，选中组件后可编辑属性和样式

### 2. 组件操作

- **添加组件**: 从左侧组件库拖拽到画布
- **选中组件**: 点击画布中的组件
- **移动组件**: 拖拽已选中的组件
- **删除组件**: 选中组件后点击删除按钮
- **编辑属性**: 在右侧属性面板中修改

### 3. 预览功能

- **预览模式**: 点击工具栏中的"预览模式"按钮
- **在线预览**: 点击"在线预览"按钮跳转到独立预览页面

### 4. 模板功能

- **登录模板**: 点击工具栏中的"模板"下拉菜单，选择"登录页面模板"

### 5. 项目管理

- **保存项目**: 点击"保存项目"按钮，数据保存到本地存储
- **清空画布**: 点击"清空画布"按钮清除所有组件

## 组件说明

### 文本组件
- 支持 HTML 内容
- 可配置标签类型（div, p, h1-h6等）
- 支持字体样式设置

### 输入框组件
- 支持多种输入类型（文本、密码、邮箱等）
- 可配置占位符和默认值
- 支持禁用状态

### 按钮组件
- 支持多种按钮类型和样式
- 可配置按钮文本
- 支持加载和禁用状态

### 图片组件
- 支持图片URL设置
- 可配置图片适应方式
- 支持占位符显示

## 扩展开发

### 添加新组件

1. 在 `src/components/RenderComponents/` 中创建新组件
2. 在 `src/components/PropertyEditors/` 中创建对应的属性编辑器
3. 在 `src/components/ComponentLibrary.vue` 中添加组件配置
4. 更新相关的映射表和导出文件

### 自定义样式

项目使用 SCSS，可以在 `src/styles/global.scss` 中添加全局样式。

## 浏览器支持

- Chrome >= 88
- Firefox >= 78
- Safari >= 14
- Edge >= 88

## 许可证

MIT License
